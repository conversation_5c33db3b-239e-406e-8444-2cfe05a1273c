const { v4: uuidv4 } = require("uuid");
const pgVectorService = require("./src/config/pgvector");

/**
 * Test script for the new LangChain PGVectorStore implementation
 * This demonstrates the standard LangChain approach as shown in the documentation
 */
async function testPGVectorStore() {
  try {
    console.log("🚀 Testing PGVectorStore with LangChain...\n");

    // Test collection name
    const collectionName = "test-collection";

    // Example documents as shown in LangChain docs
    const documents = [
      {
        pageContent: "The powerhouse of the cell is the mitochondria",
        metadata: { source: "https://example.com", category: "biology" },
      },
      {
        pageContent: "Buildings are made out of brick",
        metadata: { source: "https://example.com", category: "construction" },
      },
      {
        pageContent: "Mitochondria are made out of lipids",
        metadata: { source: "https://example.com", category: "biology" },
      },
      {
        pageContent: "The 2024 Olympics are in Paris",
        metadata: { source: "https://example.com", category: "sports" },
      },
    ];

    const ids = [uuidv4(), uuidv4(), uuidv4(), uuidv4()];

    console.log("📄 Adding documents to vector store...");
    await pgVectorService.addDocuments(collectionName, documents, { ids });
    console.log("✅ Documents added successfully!\n");

    // Test similarity search
    console.log("🔍 Testing similarity search...");
    const searchResults = await pgVectorService.similaritySearch(
      collectionName,
      "biology",
      2
    );

    console.log("Search results for 'biology':");
    for (const doc of searchResults) {
      console.log(`* ${doc.pageContent} [${JSON.stringify(doc.metadata)}]`);
    }
    console.log("");

    // Test similarity search with scores
    console.log("📊 Testing similarity search with scores...");
    const searchWithScoreResults = await pgVectorService.similaritySearchWithScore(
      collectionName,
      "biology",
      2
    );

    console.log("Search results with scores for 'biology':");
    for (const [doc, score] of searchWithScoreResults) {
      console.log(
        `* [SIM=${score.toFixed(3)}] ${doc.pageContent} [${JSON.stringify(
          doc.metadata
        )}]`
      );
    }
    console.log("");

    // Test retriever
    console.log("🎯 Testing retriever...");
    const retriever = await pgVectorService.createRetriever(collectionName, {
      k: 2,
    });

    const retrievedDocs = await retriever.invoke("mitochondria");
    console.log("Retrieved documents for 'mitochondria':");
    for (const doc of retrievedDocs) {
      console.log(`* ${doc.pageContent} [${JSON.stringify(doc.metadata)}]`);
    }
    console.log("");

    // Test collection info
    console.log("ℹ️  Getting collection info...");
    const collectionInfo = await pgVectorService.getCollectionInfo(collectionName);
    console.log("Collection info:", collectionInfo);
    console.log("");

    // Test deleting a document
    console.log("🗑️  Testing document deletion...");
    const lastId = ids[ids.length - 1];
    await pgVectorService.deleteDocuments(collectionName, { ids: [lastId] });
    console.log("✅ Document deleted successfully!\n");

    // Verify deletion
    console.log("🔍 Verifying document count after deletion...");
    const updatedInfo = await pgVectorService.getCollectionInfo(collectionName);
    console.log("Updated collection info:", updatedInfo);
    console.log("");

    // List all collections
    console.log("📋 Listing all collections...");
    const collections = await pgVectorService.listCollections();
    console.log("Collections:", collections);
    console.log("");

    console.log("🎉 All tests completed successfully!");

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    console.error(error.stack);
  } finally {
    // Clean up: close the connection
    await pgVectorService.close();
    console.log("🔒 Database connection closed.");
  }
}

// Run the test
if (require.main === module) {
  testPGVectorStore();
}

module.exports = testPGVectorStore;