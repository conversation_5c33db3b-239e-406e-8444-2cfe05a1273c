#!/usr/bin/env node
/**
 * Test script to verify chatbot can retrieve documents with sources
 */

require('module-alias/register');
require('dotenv').config();

const getVectorStore = require('./src/config/vectorStore');
const pgVectorService = require('./src/config/pgvector');

async function testChatbotSources() {
  console.log('=== Testing Chatbot Source Retrieval ===\n');
  
  try {
    // Test collection name (use one that should have data)
    const testCollections = [
      'web-1fa04585-3288d81a-4a556bb5', // This has 41 documents after migration
      // 'web-1fa04585-d587597a-9984870e', // This also has 41 documents after migration
    ];
    
    for (const collectionName of testCollections) {
      console.log(`\n--- Testing Collection: ${collectionName} ---`);
      
      try {
        // Test 1: Get vector store
        console.log('1. Getting vector store...');
        const vectorStore = await getVectorStore(collectionName);
        console.log('✓ Vector store retrieved');
        
        // Test 2: Create retriever
        console.log('2. Creating retriever...');
        const retriever = vectorStore.asRetriever({
          searchType: "similarity",
          searchKwargs: {
            k: 5,
          },
        });
        console.log('✓ Retriever created');
        
        // Test 3: Search for documents
        console.log('3. Searching for documents...');
        const testQueries = ['Passisto', 'AI', 'enterprise', 'platform'];
        
        for (const query of testQueries) {
          console.log(`\n   Searching for: "${query}"`);
          const documents = await retriever.invoke(query);
          console.log(`   Found ${documents.length} documents`);
          
          if (documents.length > 0) {
            // Show first document details
            const firstDoc = documents[0];
            console.log(`   Sample content: ${firstDoc.pageContent.substring(0, 100)}...`);
            console.log(`   Metadata: ${JSON.stringify(firstDoc.metadata, null, 2)}`);
            
            // Extract sources
            const sources = documents
              .map(doc => doc.metadata?.source)
              .filter(source => source && source.trim() !== '');
            
            console.log(`   Sources found: ${sources.length}`);
            if (sources.length > 0) {
              console.log(`   Sample sources: ${sources.slice(0, 3).join(', ')}`);
            }
          }
        }
        
        console.log(`✓ Collection ${collectionName} test completed`);
        
      } catch (error) {
        console.log(`✗ Collection ${collectionName} test failed: ${error.message}`);
      }
    }
    
    // Test 4: Direct pgvector service test
    console.log('\n--- Testing Direct PGVector Service ---');
    try {
      const collectionInfo = await pgVectorService.getCollectionInfo('web-1fa04585-3412a4f2-691df27f');
      console.log('Collection info:', collectionInfo);

      if (collectionInfo) {
        const searchResults = await pgVectorService.similaritySearch(
          'web-1fa04585-3412a4f2-691df27f',
          'Passisto platform',
          3
        );
        console.log(`Direct search found ${searchResults.length} documents`);

        searchResults.forEach((doc, index) => {
          console.log(`Document ${index + 1}:`);
          console.log(`  Content: ${doc.pageContent.substring(0, 100)}...`);
          console.log(`  Source: ${doc.metadata?.source || 'No source'}`);
        });
      }
    } catch (error) {
      console.log(`Direct service test failed: ${error.message}`);
    }
    
    console.log('\n🎉 Chatbot source retrieval tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testChatbotSources()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = testChatbotSources;
