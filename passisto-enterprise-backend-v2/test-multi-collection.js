#!/usr/bin/env node
/**
 * Test script to verify multi-collection search functionality
 */

require('module-alias/register');
require('dotenv').config();

const getVectorStore = require('./src/config/vectorStore');

async function testMultiCollectionSearch() {
  console.log('=== Testing Multi-Collection Search ===\n');
  
  try {
    // Test collections that have data
    const testCollections = [
      'web-1fa04585-3412a4f2-691df27f', // Has 41 documents
      'web-1fa04585-d587597a-9984870e', // Has 41 documents
    ];
    
    console.log(`Testing with collections: ${testCollections.join(', ')}\n`);
    
    // Simulate the multi-collection search logic from chatbot-flow.js
    const queryText = 'Passisto platform';
    let allDocuments = [];
    const documentsPerCollection = Math.ceil(5 / testCollections.length);
    
    console.log(`Documents per collection: ${documentsPerCollection}\n`);
    
    // Search each collection
    for (const collectionName of testCollections) {
      try {
        console.log(`🔍 Searching collection: ${collectionName}`);
        const vectorStore = await getVectorStore(collectionName);
        
        // Configure retriever for this collection
        const retriever = vectorStore.asRetriever({
          searchType: "similarity",
          searchKwargs: {
            k: documentsPerCollection,
          },
        });
        
        const documents = await retriever.invoke(queryText);
        console.log(`   ✅ Found ${documents.length} documents`);
        
        if (documents.length > 0) {
          // Show sample document
          const firstDoc = documents[0];
          console.log(`   📄 Sample: ${firstDoc.pageContent.substring(0, 100)}...`);
          console.log(`   🔗 Source: ${firstDoc.metadata?.source || 'No source'}`);
        }
        
        // Add collection info to metadata for tracking
        const documentsWithCollection = documents.map(doc => ({
          ...doc,
          metadata: {
            ...doc.metadata,
            collection: collectionName
          }
        }));
        
        allDocuments.push(...documentsWithCollection);
        console.log('');
        
      } catch (collectionError) {
        console.error(`   ❌ Error searching collection ${collectionName}:`, collectionError.message);
      }
    }
    
    // Summary
    console.log(`📊 Summary:`);
    console.log(`   Total documents retrieved: ${allDocuments.length}`);
    
    // Collection distribution
    const collectionStats = allDocuments.reduce((acc, doc) => {
      const collection = doc.metadata?.collection || 'unknown';
      acc[collection] = (acc[collection] || 0) + 1;
      return acc;
    }, {});
    
    console.log(`   Documents by collection:`, collectionStats);
    
    // Unique sources
    const sources = [...new Set(
      allDocuments
        .map(doc => doc.metadata?.source)
        .filter(source => source && source.trim() !== '')
    )];
    
    console.log(`   Unique sources found: ${sources.length}`);
    if (sources.length > 0) {
      console.log(`   Sample sources:`);
      sources.slice(0, 5).forEach((source, index) => {
        console.log(`     ${index + 1}. ${source}`);
      });
    }
    
    console.log('\n🎉 Multi-collection search test completed successfully!');
    
  } catch (error) {
    console.error('❌ Multi-collection search test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testMultiCollectionSearch()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = testMultiCollectionSearch;
