const dotenv = require("dotenv");
const { Pool } = require("pg");
const { PGVectorStore } = require("@langchain/community/vectorstores/pgvector");
const { AzureOpenAIEmbeddings } = require("@langchain/openai");

dotenv.config();

// Reuse a single Pool across the app
let pool;
function getPool() {
  if (!pool) {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: process.env.PG_SSL === "true" ? { rejectUnauthorized: false } : undefined,
    });
  }
  return pool;
}

// Reuse embeddings instance
let embeddings;
function getEmbeddings() {
  if (!embeddings) {
    embeddings = new AzureOpenAIEmbeddings({
      modelName: process.env.EMBEDDING_MODEL || "text-embedding-3-small",
      // Default to 1536 (embedding-3-small default). Override via EMBEDDING_DIMENSION if needed.
      dimensions: parseInt(process.env.EMBEDDING_DIMENSION || "1536", 10),
      azureOpenAIApiKey: process.env.AZURE_OPENAI_EMBEDDING_API_KEY || process.env.AZURE_OPENAI_API_KEY,
      azureOpenAIApiVersion: process.env.AZURE_OPENAI_EMBEDDING_API_VERSION || process.env.AZURE_OPENAI_API_VERSION,
      azureOpenAIApiInstanceName: process.env.AZUR_OPENAI_EMBEDDING_INSTANCE_NAME || process.env.AZURE_OPENAI_API_INSTANCE_NAME,
      azureOpenAIApiDeploymentName: process.env.AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME || process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME,
    });
  }
  return embeddings;
}

/**
 * Create and return a PGVectorStore that works with existing individual tables.
 * This avoids the need for data migration by working directly with the current schema.
 *
 * @param {string} indexName - Name of the individual table (collection)
 * @returns {Promise<PGVectorStore>} Initialized PGVectorStore instance
 */
const vectorStore = async (indexName) => {
  // Quote the table name to handle hyphens and special characters
  const quotedTableName = `"${indexName}"`;

  const store = await PGVectorStore.initialize(getEmbeddings(), {
    pool: getPool(),
    // Use the quoted collection name as the table name directly
    tableName: quotedTableName,
    // Use public schema by default
    schema: process.env.PGVECTOR_SCHEMA || "public",
    // Cosine distance matches our similarity usage
    distanceStrategy: "cosine",
    // Configure column mapping for existing schema
    columns: {
      idColumnName: "langchain_id",
      vectorColumnName: "embedding",
      contentColumnName: "content",
      metadataColumnName: "langchain_metadata",
    },
  });

  return store;
};

module.exports = vectorStore;