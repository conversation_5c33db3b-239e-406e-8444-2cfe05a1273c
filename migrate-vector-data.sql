-- Migration script to move data from individual collection tables to standard LangChain schema
-- and fix collection names

\echo 'Starting vector data migration...'

-- Step 1: Fix collection names with extra braces
\echo 'Step 1: Fixing collection names...'

UPDATE langchain_pg_collection 
SET name = TRIM(BOTH '{}' FROM TRIM(BOTH '"' FROM name))
WHERE name LIKE '{%}' OR name LIKE '"%"';

\echo 'Collection names fixed.'

-- Step 2: Show current collections
\echo 'Current collections:'
SELECT name, uuid FROM langchain_pg_collection ORDER BY name;

-- Step 3: Migrate data from web-1fa04585-3412a4f2-691df27f
\echo 'Step 3: Migrating data from web-1fa04585-3412a4f2-691df27f...'

-- Get the collection ID for web-1fa04585-3412a4f2-691df27f
DO $$
DECLARE
    collection_uuid UUID;
    doc_count INTEGER;
    migrated_count INTEGER;
BEGIN
    -- Get collection UUID
    SELECT uuid INTO collection_uuid 
    FROM langchain_pg_collection 
    WHERE name = 'web-1fa04585-3412a4f2-691df27f';
    
    IF collection_uuid IS NULL THEN
        RAISE NOTICE 'Collection not found, creating it...';
        INSERT INTO langchain_pg_collection (name, cmetadata, uuid) 
        VALUES ('web-1fa04585-3412a4f2-691df27f', '{}', gen_random_uuid())
        RETURNING uuid INTO collection_uuid;
        RAISE NOTICE 'Created collection with UUID: %', collection_uuid;
    ELSE
        RAISE NOTICE 'Found collection with UUID: %', collection_uuid;
    END IF;
    
    -- Check document count in original table
    EXECUTE 'SELECT COUNT(*) FROM "web-1fa04585-3412a4f2-691df27f"' INTO doc_count;
    RAISE NOTICE 'Found % documents in original table', doc_count;
    
    -- Clear any existing documents in langchain_pg_embedding for this collection
    DELETE FROM langchain_pg_embedding WHERE collection_id = collection_uuid;
    RAISE NOTICE 'Cleared existing documents from langchain_pg_embedding';
    
    -- Migrate documents
    EXECUTE format('
        INSERT INTO langchain_pg_embedding (text, metadata, embedding, collection_id)
        SELECT 
            content as text,
            langchain_metadata::jsonb as metadata,
            embedding,
            %L as collection_id
        FROM "web-1fa04585-3412a4f2-691df27f"
    ', collection_uuid);
    
    -- Verify migration
    SELECT COUNT(*) INTO migrated_count 
    FROM langchain_pg_embedding 
    WHERE collection_id = collection_uuid;
    
    RAISE NOTICE 'Migrated % documents to langchain_pg_embedding', migrated_count;
    
    IF migrated_count = doc_count THEN
        RAISE NOTICE 'Migration successful!';
    ELSE
        RAISE NOTICE 'Migration count mismatch! Expected %, got %', doc_count, migrated_count;
    END IF;
END $$;

-- Step 4: Migrate data from web-1fa04585-d587597a-9984870e (if it has data)
\echo 'Step 4: Migrating data from web-1fa04585-d587597a-9984870e...'

DO $$
DECLARE
    collection_uuid UUID;
    doc_count INTEGER;
    migrated_count INTEGER;
    table_exists BOOLEAN;
BEGIN
    -- Check if table exists
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'web-1fa04585-d587597a-9984870e'
    ) INTO table_exists;
    
    IF NOT table_exists THEN
        RAISE NOTICE 'Table web-1fa04585-d587597a-9984870e does not exist, skipping...';
        RETURN;
    END IF;
    
    -- Get collection UUID
    SELECT uuid INTO collection_uuid 
    FROM langchain_pg_collection 
    WHERE name = 'web-1fa04585-d587597a-9984870e';
    
    IF collection_uuid IS NULL THEN
        RAISE NOTICE 'Collection not found, creating it...';
        INSERT INTO langchain_pg_collection (name, cmetadata, uuid) 
        VALUES ('web-1fa04585-d587597a-9984870e', '{}', gen_random_uuid())
        RETURNING uuid INTO collection_uuid;
        RAISE NOTICE 'Created collection with UUID: %', collection_uuid;
    ELSE
        RAISE NOTICE 'Found collection with UUID: %', collection_uuid;
    END IF;
    
    -- Check document count in original table
    EXECUTE 'SELECT COUNT(*) FROM "web-1fa04585-d587597a-9984870e"' INTO doc_count;
    RAISE NOTICE 'Found % documents in original table', doc_count;
    
    IF doc_count = 0 THEN
        RAISE NOTICE 'No documents to migrate, skipping...';
        RETURN;
    END IF;
    
    -- Clear any existing documents in langchain_pg_embedding for this collection
    DELETE FROM langchain_pg_embedding WHERE collection_id = collection_uuid;
    RAISE NOTICE 'Cleared existing documents from langchain_pg_embedding';
    
    -- Migrate documents
    EXECUTE format('
        INSERT INTO langchain_pg_embedding (text, metadata, embedding, collection_id)
        SELECT 
            content as text,
            langchain_metadata::jsonb as metadata,
            embedding,
            %L as collection_id
        FROM "web-1fa04585-d587597a-9984870e"
    ', collection_uuid);
    
    -- Verify migration
    SELECT COUNT(*) INTO migrated_count 
    FROM langchain_pg_embedding 
    WHERE collection_id = collection_uuid;
    
    RAISE NOTICE 'Migrated % documents to langchain_pg_embedding', migrated_count;
    
    IF migrated_count = doc_count THEN
        RAISE NOTICE 'Migration successful!';
    ELSE
        RAISE NOTICE 'Migration count mismatch! Expected %, got %', doc_count, migrated_count;
    END IF;
END $$;

-- Step 5: Final verification
\echo 'Step 5: Final verification...'

SELECT 
    c.name as collection_name,
    c.uuid as collection_id,
    COUNT(e.id) as document_count
FROM langchain_pg_collection c
LEFT JOIN langchain_pg_embedding e ON c.uuid = e.collection_id
GROUP BY c.name, c.uuid
ORDER BY c.name;

\echo 'Migration completed!'
