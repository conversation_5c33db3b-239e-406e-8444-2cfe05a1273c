import os
import psycopg2
from psycopg2.extras import RealDictCursor
import numpy as np
from typing import List, Dict, Any, Optional
import json
import logging

# PostgreSQL Configuration from environment variables
DB_HOST = os.getenv("DB_HOST", "pe-db")
DB_PORT = int(os.getenv("DB_PORT", 5432))
DB_NAME = os.getenv("DB_NAME", "passisto")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "")
DATABASE_URL = os.getenv("DATABASE_URL")
EMBEDDING_DIMENSION = int(os.getenv("EMBEDDING_DIMENSION", "1024"))

class PgVectorClient:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(PgVectorClient, cls).__new__(cls)
            cls._instance.connection = None
            cls._instance._connect()
        return cls._instance
    
    def _connect(self):
        """Establish connection to PostgreSQL with pgvector"""
        try:
            if DATABASE_URL:
                self.connection = psycopg2.connect(DATABASE_URL)
            else:
                self.connection = psycopg2.connect(
                    host=DB_HOST,
                    port=DB_PORT,
                    database=DB_NAME,
                    user=DB_USER,
                    password=DB_PASSWORD
                )
            self.connection.autocommit = True
            
            # Enable pgvector extension
            with self.connection.cursor() as cursor:
                cursor.execute("CREATE EXTENSION IF NOT EXISTS vector;")
                
        except Exception as e:
            print(f"Failed to connect to PostgreSQL: {e}")
            raise
    
    def get_connection(self):
        """Get database connection"""
        if self.connection is None or self.connection.closed:
            self._connect()
        return self.connection
    
    def ensure_individual_table_exists(self, table_name: str):
        """Ensure individual table exists for this collection (like web worker)"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cursor:
                # Check if table already exists
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = %s
                    );
                """, (table_name,))

                if cursor.fetchone()[0]:
                    logging.info(f"Table {table_name} already exists")
                    return

                # Create the individual table with the same schema as web worker
                safe_table_name = f'"{table_name}"'
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {safe_table_name} (
                    langchain_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    content TEXT NOT NULL,
                    embedding vector({EMBEDDING_DIMENSION}) NOT NULL,
                    langchain_metadata JSON
                );
                """
                cursor.execute(create_table_sql)

                # Create index for better performance
                index_name = f"idx_{table_name.replace('-', '_')}_embedding"[:63]
                cursor.execute(f"""
                CREATE INDEX IF NOT EXISTS "{index_name}"
                ON {safe_table_name} USING hnsw (embedding vector_cosine_ops);
                """)

                logging.info(f"Created individual table: {table_name}")
        except Exception as e:
            logging.error(f"Error creating individual table: {e}")
            raise
    
    def index_documents(self, table_name: str, documents: List[Dict[str, Any]]):
        """Index documents with their vectors in individual table"""
        conn = self.get_connection()
        self.ensure_individual_table_exists(table_name)

        try:
            with conn.cursor() as cursor:
                safe_table_name = f'"{table_name}"'

                for doc in documents:
                    # Extract vector from document
                    vector = doc.get('vector_field', [])
                    if not vector:
                        continue

                    # Convert vector to proper format for PostgreSQL
                    vector_str = f"[{','.join(map(str, vector))}]"

                    # Prepare document content (remove vector_field for storage)
                    content_dict = {k: v for k, v in doc.items() if k != 'vector_field'}
                    content_text = content_dict.get('text', content_dict.get('content', ''))
                    metadata_json = json.dumps(content_dict.get('metadata', {}))

                    # Insert document into individual table
                    insert_sql = f"""
                    INSERT INTO {safe_table_name} (content, embedding, langchain_metadata)
                    VALUES (%s, %s::vector, %s)
                    """
                    cursor.execute(insert_sql, (content_text, vector_str, metadata_json))

                logging.info(f"Successfully indexed {len(documents)} documents in table {table_name}")

        except Exception as e:
            logging.error(f"Error indexing documents: {e}")
            raise
    
    def delete_by_source(self, table_name: str, source: str):
        """Delete documents by source from individual table"""
        conn = self.get_connection()

        try:
            with conn.cursor() as cursor:
                safe_table_name = f'"{table_name}"'
                cursor.execute(f"""
                    DELETE FROM {safe_table_name}
                    WHERE (langchain_metadata ->> 'source') = %s
                """, (source,))

                deleted_count = cursor.rowcount
                logging.info(f"Deleted {deleted_count} documents from source: {source} in table {table_name}")

        except Exception as e:
            logging.error(f"Error deleting documents by source: {e}")
            raise
    
    def search_similar(self, table_name: str, query_vector: List[float], limit: int = 10):
        """Search for similar vectors in individual table"""
        conn = self.get_connection()

        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                safe_table_name = f'"{table_name}"'
                vector_str = f"[{','.join(map(str, query_vector))}]"

                cursor.execute(f"""
                    SELECT content, langchain_metadata,
                           embedding <=> %s::vector as distance
                    FROM {safe_table_name}
                    ORDER BY embedding <=> %s::vector
                    LIMIT %s
                """, (vector_str, vector_str, limit))

                return cursor.fetchall()

        except Exception as e:
            logging.error(f"Error searching similar vectors: {e}")
            raise

def get_pgvector_client():
    """Get singleton pgvector client"""
    return PgVectorClient()