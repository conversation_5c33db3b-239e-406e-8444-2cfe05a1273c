import stat
import os
import re
import ftplib
import paramiko
import shlex
from enum import Enum
from contextlib import contextmanager
from typing import Optional, Union, Dict, Any
from dotenv import load_dotenv
from utils.Index import Index
from utils.DocLoader import Load2Database
import utils.FileParsers as FileParsers
from utils.setLogger import logger
import uuid

# File patterns
FILE_PATTERNS: Dict[str, re.Pattern] = {
    'pdf': re.compile(r'\.pdf$'),
    'excel': re.compile(r'\.xls(x)?$'),
    'powerpoint': re.compile(r'\.pptx$'),
    'word': re.compile(r'\.docx$'),
    'csv': re.compile(r'\.csv$'),
    'txt': re.compile(r'\.txt$'),
    'xml': re.compile(r'\.xml$')
}

class FTPError(Exception):
    """Base exception for FTP-related errors"""
    pass

class ConnectionError(FTPError):
    """Exception raised for connection-related errors"""
    pass

class FileProcessingError(FTPError):
    """Exception raised for file processing errors"""
    pass

class DatabaseError(FTPError):
    """Exception raised for database-related errors"""
    pass

class FileTypes(Enum):
    ALL = 'all'
    CSV = 'csv'
    XML = 'xml'

class UnifiedClient:
    def __init__(self, host: str, port: int, username: str, password: str,
                 file_types: Union[str, FileTypes], max_size: float, vector_index_name: str):
        try:
            load_dotenv()
            self.host = host
            self.port = port
            self.username = username
            self.password = password
            self.file_types = FileTypes(file_types) if isinstance(file_types, str) else file_types
            self.max_size_files = max_size
            self.table_name = vector_index_name  # Use as table name for individual table approach
            self.index_conn = Index()
            self.index_conn.create_index(self.table_name)
            self.database = Load2Database(self.table_name)
            self.client: Optional[Union[ftplib.FTP, paramiko.SFTPClient]] = None
        except Exception as e:
            raise ConnectionError(f"Failed to initialize client: {str(e)}")


    @contextmanager
    def _safe_file_operation(self, filename: str):
        """Context manager for safe file operations with cleanup"""
        local_path = os.path.join(os.getcwd(), os.path.basename(filename))
        try:
            yield local_path
        finally:
            if os.path.exists(local_path):
                try:
                    os.remove(local_path)
                    logger.log.debug(f"Cleaned up temporary file: {local_path}")
                except OSError as e:
                    logger.log.error(f"Failed to clean up file {local_path}: {str(e)}")


    def _filter_file(self, filename: str, file_size: float) -> bool:
        """Filter files based on type and size"""
        # Determine if the file type is allowed
        allowed = False
        if self.file_types == FileTypes.ALL:
            allowed = any(pattern.search(filename) for pattern in FILE_PATTERNS.values())
        else:
            pattern = FILE_PATTERNS[self.file_types.value]
            allowed = pattern.search(filename) is not None
            
        if not allowed:
            logger.log.info(f"File {filename} is not of the allowed type")
            return False

        # Check size constraints based on type
        if self.file_types == FileTypes.ALL:
            # Apply size limits only to CSV and XML
            if (FILE_PATTERNS['csv'].search(filename) or FILE_PATTERNS['xml'].search(filename)) and file_size > self.max_size_files:
                logger.log.error(f"CSV/XML file {filename} exceeds maximum size")
                return False
        else:
            if file_size > self.max_size_files:
                logger.log.error(f"{self.file_types.value.upper()} file {filename} exceeds maximum size")
                return False
        return True


    def _check_file_permissions(self, sftp_or_ftp: Union[ftplib.FTP, paramiko.SFTPClient], filename: str) -> bool:
        """Check if the file is accessible"""
        try:
            if isinstance(sftp_or_ftp, (ftplib.FTP, ftplib.FTP_TLS)):
                return True  # FTP has no direct permission check
            else:
                file_stat = sftp_or_ftp.stat(filename)
                return bool(file_stat.st_mode & 0o400)  # Check readable
        except Exception as e:
            logger.log.error(f"Permission check failed for {filename}: {str(e)}")
            return False


    def _download_file(self, sftp_or_ftp: Union[ftplib.FTP, paramiko.SFTPClient], filename: str) -> Optional[str]:
        """Download file with error handling"""
        try:
            logger.log.debug(f"Attempting to download file: {filename}")

            if not self._check_file_permissions(sftp_or_ftp, filename):
                return None
            # Get file size
            file_size = 0.0
            if isinstance(sftp_or_ftp, (ftplib.FTP, ftplib.FTP_TLS)):
                try:
                    file_size = sftp_or_ftp.size(filename) / 1e6
                except ftplib.error_perm as e:
                    logger.log.warning(f"Could not get size for {filename}: {str(e)}. Proceeding without size check.")
                    file_size = 0.0
            else:
                try:
                    file_size = sftp_or_ftp.stat(filename).st_size / 1e6
                except FileNotFoundError:
                    logger.log.error(f"File not found: {filename}")
                    return None
            # Check if file passes filters
            if not self._filter_file(filename, file_size):
                return None
            local_path = os.path.join(os.getcwd(), os.path.basename(filename))
            if isinstance(sftp_or_ftp, (ftplib.FTP, ftplib.FTP_TLS)):
                try:
                    with open(local_path, "wb") as f:
                        sftp_or_ftp.retrbinary(f"RETR {filename}", f.write)
                except ftplib.error_perm as e:
                    logger.log.error(f"Download failed for {filename}: {str(e)}")
                    return None
            else:
                try:
                    sftp_or_ftp.get(filename, local_path)
                except Exception as e:
                    logger.log.error(f"Download failed for {filename}: {str(e)}")
                    return None

            logger.log.info(f"Downloaded {filename} to {local_path}")
            return local_path

        except Exception as e:
            logger.log.error(f"Error downloading {filename}: {str(e)}")
            return None


    def traverse_directory(self, directory: str = "/") -> None:
        """Traverse directory with error handling"""
        try:
            if isinstance(self.client, (ftplib.FTP, ftplib.FTP_TLS)):
                self._traverse_ftp_directory(directory)
            else:
                self._traverse_sftp_directory(directory)
        except Exception as e:
            logger.log.error(f"Directory traversal error: {str(e)}")
            raise FTPError(f"Directory traversal failed: {str(e)}")


    def _traverse_ftp_directory(self, directory: str) -> None:
        """Improved FTP directory traversal"""
        try:
            self.client.cwd(directory)
            try:
                # Try MLSD for accurate directory listing
                files = []
                self.client.retrlines('MLSD', files.append)
                for line in files:
                    parts = line.split(';')
                    name = parts[-1].strip()
                    facts = {part.split('=')[0]: part.split('=')[1] for part in parts[:-1]}
                    if facts.get('type') == 'dir' and name not in ('.', '..'):
                        self._traverse_ftp_directory(name)
                        self.client.cwd('..')
                    else:
                        self._file_etl(name)
            except ftplib.error_perm:
                # Fallback to NLST and CWD check
                for name in self.client.nlst():
                    if name in ('.', '..'):
                        continue
                    try:
                        self.client.cwd(name)
                        self._traverse_ftp_directory(name)
                        self.client.cwd('..')
                    except ftplib.error_perm:
                        self._file_etl(name)
        except Exception as e:
            logger.log.error(f"FTP traversal error in {directory}: {str(e)}")
            raise FTPError(f"Directory traversal failed: {str(e)}")


    def _traverse_sftp_directory(self, path: str) -> None:
        """Traverse SFTP directory"""
        try:
            for attr in self.client.listdir_attr(path):
                full_path = os.path.join(path, attr.filename)
                if stat.S_ISDIR(attr.st_mode):
                    self._traverse_sftp_directory(full_path)
                else:
                    self._file_etl(full_path)
        except Exception as e:
            logger.log.error(f"SFTP traversal error in {path}: {str(e)}")


    def _file_etl(self, remote_path: str) -> None:
        """Process file with correct local path"""
        try:
            filename = os.path.basename(remote_path)
            logger.log.debug(f"Processing {remote_path}")
            local_path = self._download_file(self.client, remote_path)
            
            if not local_path or not os.path.exists(local_path):
                return
            
            # Determine parser based on file extension
            for file_type, pattern in FILE_PATTERNS.items():
                logger.log.debug(f"Checking pattern for file_type '{file_type}' against filename '{filename}'")
                if pattern.search(filename):
                    logger.log.info(f"File '{filename}' matched pattern for '{file_type}'")
                    parser = getattr(FileParsers, f"parse_{file_type}", None)
                    if parser:
                        logger.log.debug(f"Using parser 'parse_{file_type}' for file '{filename}'")
                        try:
                            logger.log.info(f"Starting parsing for '{filename}' with '{file_type}' parser")
                            docs = parser(local_path, local_path, remote_path)
                            logger.log.info(f"Parsing completed for '{filename}'")
                        except Exception as e:
                            logger.log.error(f"Error during parsing '{filename}' with '{file_type}' parser: {str(e)}")
                            continue
                        finally:
                            # Clean up file after parsing attempt
                            try:
                                os.remove(local_path)
                                logger.log.debug(f"Cleaned up temporary file: {local_path}")
                            except Exception as e:
                                logger.log.warning(f"Failed to clean up {local_path}: {str(e)}")
                            
                        if docs:
                            logger.log.info(f"Loading parsed documents for '{filename}' into database")
                            self.database.load_document(docs)
                        else:
                            logger.log.warning(f"No documents returned by parser for '{filename}'")
                        return
                    else:
                        logger.log.warning(f"No parser found for file_type '{file_type}' and file '{filename}'")
                    
            logger.log.info(f"Unsupported file format: {filename}")
            # Clean up file if no parser was found
            try:
                os.remove(local_path)
                logger.log.debug(f"Cleaned up temporary file: {local_path}")
            except Exception as e:
                logger.log.warning(f"Failed to clean up {local_path}: {str(e)}")
            
        except Exception as e:
            logger.log.error(f"Error processing {remote_path}: {str(e)}")
            raise FileProcessingError(f"File processing failed: {str(e)}")


    def close(self) -> None:
        """Cleanup resources"""
        if self.conn:
            self.cursor.close()
            self.conn.close()
            logger.log.info("Database connection closed")


class FTPClient(UnifiedClient):
    def __init__(self, host: str, port: int, username: str, password: str, 
                 file_types: str, max_size: float, vector_index_name: str):
        super().__init__(host, port, username, password, file_types, max_size, vector_index_name)
        self.ftp_client: Optional[ftplib.FTP] = None
        try:
            self.ftp_client = ftplib.FTP()
            self.ftp_client.connect(host=host, port=port)
            self.ftp_client.login(user=username, passwd=password)
            self.client = self.ftp_client
            logger.log.info("FTP connected")
        except ftplib.all_errors as e:
            raise ConnectionError(f"FTP connection failed: {str(e)}")

    def close(self) -> None:
        super().close()
        if self.ftp_client:
            self.ftp_client.quit()
            logger.log.info("FTP connection closed")


class SFTPClient(UnifiedClient):
    def __init__(self, host: str, port: int, username: str, password: str, 
                 file_types: str, max_size: float, vector_index_name: str):
        super().__init__(host, port, username, password, file_types, max_size, vector_index_name)
        self.transport: Optional[paramiko.Transport] = None
        try:
            self.transport = paramiko.Transport((host, port))
            self.transport.connect(username=username, password=password)
            self.client = paramiko.SFTPClient.from_transport(self.transport)
            logger.log.info("SFTP connected")
        except Exception as e:
            if self.transport:
                self.transport.close()
            raise ConnectionError(f"SFTP connection failed: {str(e)}")


    def close(self) -> None:
        super().close()
        if self.client:
            self.client.close()
        if self.transport:
            self.transport.close()
            logger.log.info("SFTP connection closed")
