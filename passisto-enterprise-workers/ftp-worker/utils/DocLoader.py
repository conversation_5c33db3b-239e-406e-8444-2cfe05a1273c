import os
import warnings
from .setLogger import logger
from .pgvector_client import get_pgvector_client
from langchain_openai import AzureOpenAIEmbeddings

AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL")
EMBEDDING_DIMENSION = int(os.getenv("EMBEDDING_DIMENSION", 1024))

MAX_BULK_SIZE = int(os.getenv("MAX_BULK_SIZE", 50))
BATCH_SIZE = int(os.getenv("BATCH_SIZE", 30))

warnings.filterwarnings('ignore')


class Load2Database:

    def __init__(self, table_name):
        self._initialize_embeddings()
        self._establish_connection(table_name)
        
    
    def _initialize_embeddings(self):
        logger.log.info('Loading vector embedding model...')
        self.embeddings = AzureOpenAIEmbeddings(
            model=EMBEDDING_MODEL,
            dimensions=EMBEDDING_DIMENSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_API_KEY,
            openai_api_version=AZURE_OPENAI_API_VERSION
        )
    
    
    def _establish_connection(self, table_name):
        logger.log.info('Establishing connection to pgvector...')
        self.pgvector_client = get_pgvector_client()
        self.table_name = table_name

        # Ensure individual table exists
        self.pgvector_client.ensure_individual_table_exists(table_name)
        logger.log.info('Successfully established pgvector connection')

    
    def load_document(self, docs):
        """Load documents into pgvector database"""
        try:
            if len(docs) > MAX_BULK_SIZE:
                logger.log.debug(f'Processing {len(docs)} documents in batches...')
                batched_docs = self._batch_documents(docs)
                for i, batch in enumerate(batched_docs):
                    self._add_documents(batch, i)
            else:
                self._add_documents(docs)
        except Exception as e:
            logger.log.error(f'Error loading documents: {e}')
            raise
    
    
    def _batch_documents(self, docs):
        return [docs[i:i + BATCH_SIZE] for i in range(0, len(docs), BATCH_SIZE)]
    
    
    def _add_documents(self, docs, batch_index=None):
        """Add documents to pgvector"""
        # Convert langchain documents to pgvector format
        pgvector_docs = []
        
        for doc in docs:
            # Generate embedding for the document
            embedding = self.embeddings.embed_query(doc.page_content)
            
            # Create document structure for individual table
            pgvector_doc = {
                'vector_field': embedding,
                'text': doc.page_content,
                'content': doc.page_content,
                'metadata': doc.metadata
            }
            pgvector_docs.append(pgvector_doc)

        # Index documents in individual table
        self.pgvector_client.index_documents(self.table_name, pgvector_docs)
        
        if batch_index is not None:
            logger.log.debug(f'Batch {batch_index+1} was loaded successfully')
        else:
            logger.log.info(f'Loaded {len(docs)} documents successfully')
