from .setLogger import logger
from .pgvector_client import get_pgvector_client
from os import getenv

# pgvector configuration
EMBEDDING_DIMENSION = int(getenv("EMBEDDING_DIMENSION", 1024)) 

class Index():

    def __init__(self) -> None:
        self.pgvector_client = get_pgvector_client()
        
        
    def create_index(self, table_name):
        """Create individual table for this collection"""
        try:
            self.pgvector_client.ensure_individual_table_exists(table_name)
            logger.log.info(f'Individual table {table_name} ensured')
        except Exception as e:
            logger.log.error(f'Error creating individual table {table_name}: {e}')
            raise

    
    def truncate_source_documents(self, source, table_name):
        """
        Delete documents that belong to a specific source from individual table.
            - args :
              source : the source which will be deleted
              table_name : the target table which will be updated
        """
        try:
            self.pgvector_client.delete_by_source(table_name, source)
            logger.log.debug("related docs are truncated")
        except Exception as e:
            logger.log.error(f"Error truncating source documents: {e}")
            # Don't raise - this might be a new file to load after update